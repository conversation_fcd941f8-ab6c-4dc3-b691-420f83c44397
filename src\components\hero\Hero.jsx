"use client";

import React from 'react';

const Hero = () => {
  return (
    <section className="w-full mx-auto px-4 lg:px-[97px] py-16 lg:py-24" style={{ background: '#FFFEFB' }}>
      <div className="max-w-[1440px] mx-auto">
        <div className="flex flex-col items-start justify-start">
        {/* Content */}
        <div className="max-w-[650px]">
          {/* Main Title */}
          <h1 
            className="text-[#4B935E] mb-8 lg:mb-12"
            style={{
              fontFamily: 'Poppins, sans-serif',
              fontWeight: 700,
              fontSize: 'clamp(32px, 4vw, 52px)',
              lineHeight: 'clamp(40px, 5vw, 70px)',
              letterSpacing: '0%'
            }}
          >
            Présentation de l'organisation
          </h1>

          {/* Description */}
          <p 
            className="text-[#1F2421] mb-8 lg:mb-12"
            style={{
              fontFamily: 'Poppins, sans-serif',
              fontWeight: 500,
              fontSize: 'clamp(18px, 2vw, 24px)',
              lineHeight: 'clamp(28px, 3vw, 37px)',
              letterSpacing: '0%'
            }}
          >
            L'organisation de l'Homme Algérien est une organisation caritative à but non lucratif spécialisée dans le travail humanitaire et éducatif. Ce nom a été choisi pour refléter la véritable nature du peuple algérien, qui tend la main à ses frères dans le besoin, et pour donner une image positive de l'Algérie et de ses habitants auprès des autres peuples.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-start gap-[30px] w-full max-w-[538px]">
            <button 
              className="bg-[#4B935E] text-white px-8 py-4 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 flex items-center gap-2 w-full sm:w-auto"
              style={{
                fontFamily: 'Poppins, sans-serif',
                fontWeight: 500,
                fontSize: '16px',
                height: '67px'
              }}
            >
              Contribuer
              <svg 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
                className="ml-1"
              >
                <path 
                  d="M5 12H19M19 12L12 5M19 12L12 19" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            <button 
              className="border-2 border-[#4B935E] text-[#4B935E] px-8 py-4 rounded-lg font-medium hover:bg-[#4B935E] hover:text-white transition-colors duration-200 w-full sm:w-auto"
              style={{
                fontFamily: 'Poppins, sans-serif',
                fontWeight: 500,
                fontSize: '16px',
                height: '67px'
              }}
            >
              en savoir plus
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
