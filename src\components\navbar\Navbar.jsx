"use client";

import React, { useState } from "react";
import Image from "next/image";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav
      className="w-full mx-auto h-auto lg:h-[100.44px] mt-[25px] mb-4 rounded-2xl border border-[#E5E7EB] bg-[#FFFEFB] px-4 lg:px-[50px] py-[17px] flex flex-col lg:flex-row items-center justify-between gap-4 lg:gap-[117px]"
      style={{
        width: "1287px",
        height: "100.44264221191406px",
        borderRadius: "16px",
        paddingTop: "17px",
        paddingRight: "50px",
        paddingBottom: "17px",
        paddingLeft: "50px",
        gap: "117px",
        opacity: 1,
        borderWidth: "1px",
        position: "relative",
        left: "76.5px",
      }}
    >
      {/* Header with Logo and Mobile Menu Button */}
      <div className="flex items-center justify-between w-full lg:w-auto">
        {/* Logo */}
        <div className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={40}
            height={40}
            className="w-10 h-10"
          />
        </div>

        {/* Mobile Menu Button */}
        <button
          className="lg:hidden p-2"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label="Toggle menu"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-gray-700"
          >
            <path
              d="M3 12H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 6H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 18H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      {/* Navigation Links */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8 w-full lg:w-auto`}
      >
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Accueil
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          À propos
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Projets
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Activités
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Contact
        </a>
      </div>

      {/* Action Buttons */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-3 lg:space-y-0 lg:space-x-3 w-full lg:w-auto`}
      >
        <button className="bg-[#4B935E] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full lg:w-auto">
          Sign up
        </button>
        <button className="border border-[#E5E7EB] text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 w-full lg:w-auto">
          Log in
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
