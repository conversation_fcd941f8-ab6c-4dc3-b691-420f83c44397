"use client";

import React from "react";
import Image from "next/image";

const Card = ({
  heading,
  description,
  image,
  imageAlt = "",
  buttonText = "Contribuer",
  onButtonClick,
  isComingSoon = false,
  className = "",
  ...props
}) => {
  return (
    <div
      className={`
        w-full max-w-[384px] h-[420px]
        bg-white
        border border-[#E5E7EB]
        rounded-lg
        p-6
        flex flex-col
        gap-6
        shadow-sm hover:shadow-md transition-shadow duration-200
        relative
        ${className}
      `}
      style={{
        boxShadow:
          "0px 2px 4px -2px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.1)",
      }}
      {...props}
    >
      {/* Blur overlay for locked cards */}
      {isComingSoon && (
        <div
          className="absolute inset-0 rounded-lg"
          style={{
            background:
              "linear-gradient(142.78deg, rgba(255, 255, 255, 0.5) -2.1%, rgba(222, 237, 223, 0.5) 109.52%)",
            borderTop: "1px solid #E5E7EB",
            backdropFilter: "blur(7px)",
            zIndex: 10,
          }}
        />
      )}
      {/* Image Section */}
      {image && (
        <div className="relative w-full h-[200px] rounded-lg overflow-hidden bg-gray-100">
          <Image src={image} alt={imageAlt} fill className="object-cover" />
          {isComingSoon && (
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{ zIndex: 20 }}
            >
              <Image
                src="/locked.svg"
                alt="Locked"
                width={40}
                height={40}
                className="opacity-80"
              />
            </div>
          )}
        </div>
      )}

      {/* Content Section */}
      <div className="flex-1 flex flex-col gap-3">
        {heading && (
          <h3
            className="text-[#1F2421] font-semibold text-lg leading-6"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 600,
            }}
          >
            {heading}
          </h3>
        )}

        {description && (
          <p
            className="text-[#6B7280] text-sm leading-5 flex-1"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 400,
            }}
          >
            {description}
          </p>
        )}
      </div>

      {/* Button Section */}
      {buttonText && !isComingSoon && (
        <button
          onClick={onButtonClick}
          className="bg-[#4B935E] text-white font-medium hover:bg-[#3d7a4e] transition-colors duration-200 flex items-center justify-center"
          style={{
            width: "141px",
            height: "40px",
            borderRadius: "8px", // rounded-base
            paddingTop: "10px", // spacing/2.5
            paddingRight: "16px", // spacing/4
            paddingBottom: "10px", // spacing/2.5
            paddingLeft: "16px", // spacing/4
            gap: "6px",
            opacity: 1,
            borderWidth: "1px",
            fontFamily: "Poppins, sans-serif",
            fontWeight: 500,
            fontSize: "14px",
          }}
        >
          {buttonText}
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5 12H19M19 12L12 5M19 12L12 19"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

export default Card;
