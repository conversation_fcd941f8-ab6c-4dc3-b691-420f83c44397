import React, { useState } from "react";
import Image from "next/image";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="w-full max-w-[1287px] mx-auto h-auto lg:h-[100px] mt-[25px] rounded-2xl border border-[#E5E7EB] bg-[#FFFEFB] px-4 lg:px-[50px] py-[17px] flex flex-col lg:flex-row items-center justify-between gap-4 lg:gap-[117px]">
      {/* Header with Logo and Mobile Menu Button */}
      <div className="flex items-center justify-between w-full lg:w-auto">
        {/* Logo */}
        <div className="flex items-center">
          <div className="w-10 h-10 bg-[#4B935E] rounded-lg flex items-center justify-center">
            {/* You can replace this with your actual logo */}
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-white"
            >
              <path
                d="M12 2L2 7L12 12L22 7L12 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M2 17L12 22L22 17"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M2 12L12 17L22 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="lg:hidden p-2"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label="Toggle menu"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-gray-700"
          >
            <path
              d="M3 12H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 6H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 18H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      {/* Navigation Links */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8 w-full lg:w-auto`}
      >
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Accueil
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          À propos
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Projets
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Activités
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Contact
        </a>
      </div>

      {/* Action Buttons */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-3 lg:space-y-0 lg:space-x-3 w-full lg:w-auto`}
      >
        <button className="bg-[#4B935E] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full lg:w-auto">
          Sign up
        </button>
        <button className="border border-[#E5E7EB] text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 w-full lg:w-auto">
          Log in
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
