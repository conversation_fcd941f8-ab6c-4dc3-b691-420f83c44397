"use client";

import React from "react";
import { Card } from "../ui";

const CommentAider = () => {
  const cardsData = [
    {
      id: 1,
      heading: "Projet de sacrifices",
      description:
        "Offrez un sacrifice pour nourrir les familles dans le besoin durant l'Aïd",
      image: "/placeholder-sheepjpg.jpg",
      imageAlt: "Sheep for sacrifice project",
      buttonText: "Contribuer",
      isComingSoon: false,
    },
    {
      id: 2,
      heading: "Projet éducatif",
      description: "Soutenez l'éducation des enfants défavorisés en Algérie",
      image: "/placeholder-education.svg",
      imageAlt: "Education project",
      buttonText: "Contribuer",
      isComingSoon: true,
    },
    {
      id: 3,
      heading: "Aide humanitaire",
      description:
        "Participez à nos missions d'aide humanitaire pour les familles en détresse",
      image: "/placeholder-humanitarian.svg",
      imageAlt: "Humanitarian aid project",
      buttonText: "Contribuer",
      isComingSoon: true,
    },
  ];

  const handleCardClick = (cardId) => {
    console.log(`Card ${cardId} clicked`);
    // Add your navigation or action logic here
  };

  return (
    <section className="w-full mx-auto px-4 lg:px-[98px] py-16 lg:py-20">
      <div className="max-w-[1440px] mx-auto">
        {/* Section Title */}
        <div className="text-center mb-12 lg:mb-16">
          <h2
            className="text-[#4B935E] mx-auto"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 700,
              fontSize: "clamp(32px, 4vw, 45px)",
              lineHeight: "clamp(40px, 5vw, 70px)",
              letterSpacing: "0%",
              maxWidth: "386px",
            }}
          >
            Comment Aider
          </h2>
        </div>

        {/* Cards Container */}
        <div className="flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-[45px] max-w-[1243px] mx-auto">
          {cardsData.map((card) => (
            <Card
              key={card.id}
              heading={card.heading}
              description={card.description}
              image={card.image}
              imageAlt={card.imageAlt}
              buttonText={card.buttonText}
              isComingSoon={card.isComingSoon}
              onButtonClick={() => handleCardClick(card.id)}
              className="flex-shrink-0"
            />
          ))}
        </div>

        {/* Navigation Dots (Optional) */}
        <div className="flex justify-center items-center gap-2 mt-8 lg:hidden">
          {cardsData.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                index === 0 ? "bg-[#4B935E]" : "bg-gray-300"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
          <button
            className="ml-2 w-8 h-8 rounded-full bg-[#4B935E] text-white flex items-center justify-center hover:bg-[#3d7a4e] transition-colors duration-200"
            aria-label="Next slide"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default CommentAider;
